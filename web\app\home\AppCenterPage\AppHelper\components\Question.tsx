import { COMMON } from '@/app/home/<USER>'
import { useRouter, useSearchParams } from 'next/navigation'
export default function Question() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const appId = searchParams.get('appId')
  return (
    <>
      <div className='w-[900px] font-[PingFangSC] text-[14px] leading-[20px] text-[#181818]'>
        <div className='pb-[20px] font-[500]'>
           问题推荐
        </div>
        <div className='flex flex-col gap-[12px]'>
          {
            COMMON.appCenterHelperQuestions.map((item, index) => {
              return (
                <div key={index} className='w-full cursor-pointer truncate rounded-[13px] bg-[#FFFFFF] px-[20px] py-[11px] font-[400]' onClick={() => {
                  router.push(`/home/<USER>/${appId || COMMON.appId}?question=${encodeURIComponent(item.question)}`)
                }}>
                  {item.question}
                </div>
              )
          })
          }
        </div>
      </div>
    </>
  )
}

'use client'
import type { ConversationItem } from '@/models/share'
import { fetchConversations } from '@/service/share'
import { useCallback, useEffect, useRef, useState } from 'react'
import { Button, List, Popover, Spin, Tooltip, message } from 'antd'
import { DeleteOutlined, EditOutlined, MoreOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import RenameModal from './components/Rename'
import { delConversation, renameConversation } from '@/service/share'
import { COMMON } from '../common'
import Confirm from '@/app/components/base/confirm'
import { useDataReload } from '@/app/home/<USER>/useDataReload'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'

const INITIAL_LIMIT = 30

// 分组函数
function groupByDate(items: any[]) {
  const groups: Record<string, any[]> = {}
  const now = dayjs()
  items.forEach((item) => {
    const date = dayjs.unix(item.created_at)
    let group = ''
    if (date.isSame(now, 'day'))
      group = '今天'
     else if (now.diff(date, 'day') === 1)
      group = '昨天'
     else if (now.diff(date, 'day') <= 7)
      group = '近7天'
     else if (now.diff(date, 'day') <= 30)
      group = '近30天'
     else
      group = '30天以前'
    if (!groups[group])
      groups[group] = []
    groups[group].push(item)
  })
  return groups
}

export default function HistoryPage() {
  const router = useRouter()
  const [messageApi, contextHolder] = message.useMessage()
  const [history, setHistory] = useState<ConversationItem[]>([])
  const [loading, setLoading] = useState(false)
  const [loadingMore, setLoadingMore] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [lastId, setLastId] = useState<string | undefined>(undefined)
  const [showDialog, setShowDialog] = useState(false)
  const [dialogType, setDialogType] = useState<'rename' | 'delete'>('rename')
  const [name, setName] = useState('')
  const [conversationId, setConversationId] = useState('')
  const { t } = useTranslation()
  const loadMoreRef = useRef<HTMLDivElement>(null)

  // 初始加载数据
  const loadHistoryData = useCallback(() => {
    setLoading(true)
    setHistory([])
    setLastId(undefined)
    setHasMore(true)
    fetchConversations(true, COMMON.appId, undefined, false, INITIAL_LIMIT)
      .then((res) => {
        setHistory(res.data || [])
        setHasMore(res.has_more)
          setLastId(res.data[res.data.length - 1].id)
      })
      .catch((err) => {
        if (err.isHomePageUnauth && window.showHomeLoginDialog)
          window.showHomeLoginDialog()
      })
      .finally(() => setLoading(false))
  }, [])

  // 重命名
  const rename = useCallback(async (newName: string) => {
    await renameConversation(true, COMMON.appId, conversationId, newName)
      .catch((err) => {
        if (err.isHomePageUnauth && window.showHomeLoginDialog)
          window.showHomeLoginDialog()
      })
    setShowDialog(false)
    // 刷新历史
    loadHistoryData()
    messageApi.open({
      type: 'success',
      content: '重命名成功',
    })
  }, [conversationId, loadHistoryData, messageApi])

  // 删除
  const deleteItem = useCallback(async () => {
    await delConversation(true, COMMON.appId, conversationId)
      .catch((err) => {
        if (err.isHomePageUnauth && window.showHomeLoginDialog)
          window.showHomeLoginDialog()
      })
    setShowDialog(false)
    // 刷新历史
    loadHistoryData()
    messageApi.open({
      type: 'success',
      content: '删除成功',
    })
  }, [conversationId, loadHistoryData, messageApi])

  // 加载更多数据
  const loadMoreData = useCallback(async () => {
    if (!hasMore || loadingMore || !lastId) return

    setLoadingMore(true)
    try {
      const res = await fetchConversations(true, COMMON.appId, lastId, false, INITIAL_LIMIT)
      if (res.data && res.data.length > 0) {
        setHistory(prev => [...prev, ...res.data])
        setLastId(res.data[res.data.length - 1].id)
        setHasMore(res.has_more)
      }
      else {
        setHasMore(false)
      }
    }
    catch (err: any) {
      if (err.isHomePageUnauth && window.showHomeLoginDialog)
        window.showHomeLoginDialog()
      }
      finally {
        setLoadingMore(false)
      }
  }, [hasMore, loadingMore, lastId])

  useEffect(() => {
    loadHistoryData()
  }, [loadHistoryData])

  // 监听登录成功后的数据重新加载事件
  useDataReload(loadHistoryData)

  // IntersectionObserver 监听滚动到底部
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loadingMore && !loading)
          loadMoreData()
      },
      { rootMargin: '100px' },
    )

    if (loadMoreRef.current)
      observer.observe(loadMoreRef.current)

    return () => {
      if (loadMoreRef.current)
        observer.unobserve(loadMoreRef.current)

      observer.disconnect()
    }
  }, [hasMore, loadingMore, loading, loadMoreData])

  const grouped = groupByDate(history)
  const groupOrder = ['今天', '昨天', '近7天', '近30天', '30天以前']

  return (
    <>
      {contextHolder}
      <div className="font-[PingFang SC] size-full p-4">
        <span className="text-[20px] font-[500] leading-[28px] text-[#000000]">
          历史记录
        </span>
        {groupOrder
          .filter(group => grouped[group])
          .map(group => (
            <div key={group}>
              <span className="text-[14px] font-[500] leading-[20px] text-[#979797]">
                {group}
              </span>
              <List
                itemLayout="horizontal"
                loading={loading && history.length === 0}
                dataSource={grouped[group]}
                className='cursor-pointer'
                renderItem={item => (
                  <List.Item
                    actions={[
                      // <Tooltip title="收藏" key="star">
                      //   {item.status === 'star' ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
                      // </Tooltip>,
                      <Popover
                        content={
                          <div className="flex flex-col gap-2">
                            <Button
                              type="text"
                              icon={<EditOutlined />}
                              className="justify-start"
                              onClick={() => {
                                setDialogType('rename')
                                setShowDialog(true)
                                setName(item.name)
                                setConversationId(item.id)
                              }}
                            >
                              重命名
                            </Button>
                            <Button
                              type="text"
                              icon={<DeleteOutlined />}
                              danger
                              className="justify-start"
                              onClick={() => {
                                setDialogType('delete')
                                setShowDialog(true)
                                setConversationId(item.id)
                              }}
                            >
                              删除
                            </Button>
                          </div>
                        }
                        trigger="click"
                        key="more"
                      >
                        <Button type="text" icon={<MoreOutlined />} />
                      </Popover>,
                    ]}
                  >
                    <List.Item.Meta
                      title={
                        <Tooltip title={item.name}>
                          <span className="inline-block max-w-[300px] truncate text-[16px] font-[500] leading-[22px] text-[#191919]" onClick={() => {
                      router.push(`/home/<USER>/${COMMON.appId}?conversationId=${item.id}`)
                    }}>
                            {item.name}
                          </span>
                        </Tooltip>
                      }
                      description={
                        <Tooltip title={item.introduction || '无摘要'}>
                          <span className="inline-block max-w-[400px] truncate text-[14px] font-[400] leading-[20px] text-[#656565]" onClick={() => {
                      router.push(`/home/<USER>/${COMMON.appId}?conversationId=${item.id}`)
                    }}>
                            {item.introduction || '无摘要'}
                          </span>
                        </Tooltip>
                      }
                    />
                  </List.Item>
                )}
              />
            </div>
          ))}

        {/* 加载更多指示器 */}
        {hasMore && (
          <div
            ref={loadMoreRef}
            className="flex justify-center py-4"
            style={{
              opacity: loadingMore ? 1 : 0,
              transition: 'opacity 0.3s ease-in-out',
            }}
          >
            <Spin size="small" />
            <span className="ml-2 text-[14px] text-[#979797]">加载中...</span>
          </div>
        )}

        {!hasMore && history.length > 0 && (
          <div className="flex justify-center py-4">
            <span className="text-[14px] text-[#979797]">没有更多数据了</span>
          </div>
        )}
        {
          dialogType === 'rename' && (
             <RenameModal
            isShow={showDialog}
            saveLoading={false}
            name={''}
            conversationName={name}
            onClose={() => setShowDialog(false)}
            onSave={(newName) => {
              rename(newName)
            }}
          />
          )
        }
        {
          dialogType === 'delete' && (
            <Confirm
            title={t('share.chat.deleteConversation.title')}
            content={t('share.chat.deleteConversation.content') || ''}
            isShow={showDialog}
            onCancel={() => setShowDialog(false)}
            onConfirm={() => {
              deleteItem()
              setShowDialog(false)
            }}
          />
          )
        }
      </div>
    </>
  )
}

import { useEffect, useMemo, useRef, useState } from 'react'
import type { FC } from 'react'
import { useTranslation } from 'react-i18next'
import { RiArrowDownSLine } from '@remixicon/react'
import type { CitationItem } from '../type'
import { COMMON } from './common'
import Popup from './popup'

export type Resources = {
  documentId: string
  documentName: string
  dataSourceType: string
  sources: CitationItem[]
}

type CitationProps = {
  data: CitationItem[]
  showHitInfo?: boolean
  containerClassName?: string
}
const Citation: FC<CitationProps> = ({
  data,
  showHitInfo,
  containerClassName = 'chat-answer-container',
}) => {
  const { t } = useTranslation()
  const elesRef = useRef<HTMLDivElement[]>([])
  const [limitNumberInOneLine, setLimitNumberInOneLine] = useState(0)
  const [showMore, setShowMore] = useState(false)
  const resources = useMemo(() => data.reduce((prev: Resources[], next) => {
    // console.log(data)
    const documentId = next.document_id
    const documentName = next.document_name
    const dataSourceType = next.data_source_type
    const documentIndex = prev.findIndex(i => i.documentId === documentId)

    if (documentIndex > -1) {
      prev[documentIndex].sources.push(next)
    }
    else {
      prev.push({
        documentId,
        documentName,
        dataSourceType,
        sources: [next],
      })
    }

    return prev
  }, []), [data])

  const handleAdjustResourcesLayout = () => {
    const containerWidth = document.querySelector(`.${containerClassName}`)!.clientWidth - 40
    let totalWidth = 0
    for (let i = 0; i < resources.length; i++) {
      totalWidth += elesRef.current[i].clientWidth

      if (totalWidth + i * 4 > containerWidth!) {
        totalWidth -= elesRef.current[i].clientWidth

        if (totalWidth + 34 > containerWidth!)
          setLimitNumberInOneLine(i - 1)
        else
          setLimitNumberInOneLine(i)

        break
      }
      else {
        setLimitNumberInOneLine(i + 1)
      }
    }
  }

  useEffect(() => {
    handleAdjustResourcesLayout()
  }, [])

  const resourcesLength = resources.length
  // console.log(resources)

  return (
    // 文件部分
    <div className="-mb-1 mt-3 w-full max-w-[500px] rounded-[11px] bg-[#FFFFFF] p-[30px] font-[PingFangSC] text-[14px] leading-[20px] text-[#000818]">
      {/* 介绍标题 */}
      <div className="system-xs-medium mb-2 flex items-center text-text-tertiary">
        {COMMON.title}
        {/* <div className='ml-2 h-[1px] grow bg-divider-regular' /> */}
      </div>
      {/* 文件集合 */}
      <div className='flex w-full justify-center'>
        <div className='relative flex w-full flex-col items-center rounded-[7px] py-[15px]' style= { {
          background: 'linear-gradient( 180deg, #E4EDFB 0%, #F4F8FF 100%)',
          boxShadow: '0px 5px 18px 0px rgba(230,237,246,0.56)',
        } } >
          <div className='w-[90%] pb-[10px]'>
            【知识库】
          </div>
          <div className='relative flex w-[90%] flex-col gap-[10px] rounded-[3px] bg-[#FFFFFF] p-[10px]'>
            {/* 文件列表 */}
              {resources.map((res, index) => (
                  <div
                    className="absolute left-0 top-0 -z-10 mb-1 mr-1 h-7 w-auto max-w-[240px] whitespace-nowrap pl-7 pr-2 leading-[24px] opacity-0"
                    ref={(ele: any) => (elesRef.current[index] = ele!)}
                  >
                    {res.documentName}
                  </div>
              ))}
             {/* 详情弹窗 */}
            {resources
             .slice(0, showMore ? resourcesLength : limitNumberInOneLine)
             .map((res, index) => (
               <div key={index} className="mb-1 mr-1 cursor-pointer">
                <Popup data={res} showHitInfo={showHitInfo} />
               </div>
            ))}
            {/* 隐藏文件显示 */}
             {limitNumberInOneLine < resourcesLength && (
               <div
                  className="system-xs-medium flex h-7 cursor-pointer items-center rounded-lg bg-components-panel-bg px-2 text-text-tertiary"
                  onClick={() => setShowMore(v => !v)}
               >
                {!showMore ? (
                 `+ ${resourcesLength - limitNumberInOneLine}`
                ) : (
                  <RiArrowDownSLine className="h-4 w-4 rotate-180 text-text-tertiary" />
            )}
          </div>
        )}
        </div>
      </div>
      </div>
    </div>
  )
}

export default Citation

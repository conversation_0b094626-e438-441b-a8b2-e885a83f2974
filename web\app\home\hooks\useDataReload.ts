import { useEffect } from 'react'

/**
 * 自定义Hook：监听登录成功后的数据重新加载事件
 * @param reloadCallback 数据重新加载的回调函数
 */
export const useDataReload = (reloadCallback: () => void) => {
  useEffect(() => {
    const handleDataReload = () => {
      reloadCallback()
    }

    window.addEventListener('homeDataReload', handleDataReload)
    return () => {
      window.removeEventListener('homeDataReload', handleDataReload)
    }
  }, [reloadCallback])
}

export default useDataReload
